using UnityEngine;

namespace Spine.Unity
{
    public class SpineAnimationEvent : MonoBehaviour
    {
        private SkeletonAnimation _SkeletonAnimation;
        private SkeletonGraphic _SkeletonGraphic;

        void Awake()
        {
            _SkeletonAnimation = GetComponent<SkeletonAnimation>();
            _SkeletonGraphic = GetComponent<SkeletonGraphic>();
        }

        public void PlaySpineAnimation(string animationname, bool clear_track)
        {
            if (_SkeletonAnimation)
            {
                if (clear_track) _SkeletonAnimation.state.ClearTrack(0);
                
                _SkeletonAnimation.autoUpdate2 = true;
                _SkeletonAnimation.ClearState();
                _SkeletonAnimation.skeleton.SetSlotsToSetupPose();
                _SkeletonAnimation.AnimationName = null;
                _SkeletonAnimation.AnimationName = animationname;
                _SkeletonAnimation.Update(0);
                // _SkeletonAnimation.state.SetAnimation(0, animationname, false);
            }

            // if (_SkeletonGraphic)
            // {
            //     if (clear_track) _SkeletonGraphic.AnimationState.ClearTrack(0);
            //     _SkeletonGraphic.Skeleton.SetSlotsToSetupPose();
            //     _SkeletonGraphic.AnimationState. = null;
            //     _SkeletonGraphic.AnimationName = animationname;
            //     _SkeletonGraphic.Update(0);
            //     _SkeletonGraphic.AnimationState.SetAnimation(0, animationname, false);
            // }
        }


        public void PlaySpineAnimationLoop(string animationname, bool clear_track)
        {
            if (_SkeletonAnimation)
            {
                if (clear_track) _SkeletonAnimation.state.ClearTrack(0);
                _SkeletonAnimation.state.SetAnimation(0, animationname, true);
            }

            if (_SkeletonGraphic)
            {
                if (clear_track) _SkeletonGraphic.AnimationState.ClearTrack(0);
                _SkeletonGraphic.AnimationState.SetAnimation(0, animationname, true);
            }
        }

        public void PlaySpineAnimation(string animationName01, bool clearTrack, bool loop01, string animationName02, bool loop02)
        {
            if (_SkeletonAnimation)
            {
                if (clearTrack) _SkeletonAnimation.state.ClearTrack(0);
                _SkeletonAnimation.autoUpdate2 = true;
                _SkeletonAnimation.ClearState();
                _SkeletonAnimation.skeleton.SetSlotsToSetupPose();
                _SkeletonAnimation.AnimationName = null;
                _SkeletonAnimation.AnimationName = animationName01;
                _SkeletonAnimation.loop = loop01;
                _SkeletonAnimation.Update(0);
            }
            
            if (_SkeletonGraphic)
            {
                if (clearTrack) _SkeletonGraphic.AnimationState.ClearTrack(0);
                _SkeletonGraphic.AnimationState.SetEmptyAnimation(0, 0);
                _SkeletonGraphic.Skeleton.SetSlotsToSetupPose();
                _SkeletonGraphic.AnimationState.ClearTrack(0);
                _SkeletonGraphic.AnimationState.SetAnimation(0, animationName01, loop01);
                _SkeletonGraphic.Update(0);
            }
        }
    }
}

